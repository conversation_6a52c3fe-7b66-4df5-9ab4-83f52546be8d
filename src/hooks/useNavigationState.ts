import { useState, useEffect, useCallback } from 'react';
import { useCertificate } from '../contexts/CertificateContext';
import { supabase } from '../lib/supabase';

// Define page types
export type PageType = 'objektdaten' | 'gebaeudedetails1' | 'gebaeudedetails2' | 'fenster' | 'heizung' | 'tww-lueftung' | 'verbrauch' | 'zusammenfassung';

// Define certificate types
export type CertificateType = 'WG/V' | 'WG/B' | 'NWG/V';

// Utility function to check if a status is payment-related (known or unknown)
const isPaymentRelatedStatus = (status: string): { isPayment: boolean; isKnown: boolean; isUnknown: boolean } => {
  const knownPaymentStatuses = ['payment_initiated', 'payment_complete', 'payment_failed', 'payment_disputed', 'payment_expired'];
  const isKnown = knownPaymentStatuses.includes(status);
  const isUnknown = status.startsWith('payment_') && !isKnown;
  return {
    isPayment: isKnown || isUnknown,
    isKnown,
    isUnknown
  };
};

// Navigation state interface - now derived from database status
interface NavigationState {
  visitedPages: PageType[];
  highestPageReached: number;
  certificateId: string;
  currentPage: PageType;
  actualDbStatus: string; // The actual status from database (including payment statuses)
  timestamp: number;
}

// Define the page configuration for each certificate type
const certificateTypePages: Record<CertificateType, PageType[]> = {
  'WG/V': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'verbrauch',
    'zusammenfassung'
  ],
  'WG/B': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'fenster',
    'heizung',
    'tww-lueftung',
    'zusammenfassung'
  ],
  'NWG/V': [
    'objektdaten',
    'gebaeudedetails1',
    'gebaeudedetails2',
    'verbrauch',
    'zusammenfassung'
  ]
};

/**
 * Custom hook to manage navigation state for certificate editing flow
 * Now uses database-backed status tracking instead of session storage
 */
export const useNavigationState = (certificateType: CertificateType | null) => {
  const { activeCertificateId } = useCertificate();
  const [navigationState, setNavigationState] = useState<NavigationState | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load navigation state from database status
  const loadNavigationStateFromDatabase = useCallback(async (certId: string, certType: CertificateType): Promise<NavigationState | null> => {
    try {
      const { data, error } = await supabase
        .from('energieausweise')
        .select('status')
        .eq('id', certId)
        .single();

      if (error) {
        console.error('Error loading certificate status:', error);
        return null;
      }

      if (!data || !data.status) {
        return null;
      }

      const dbStatus = data.status;
      const pages = certificateTypePages[certType];

      // Check if this is a payment-related status (known or unknown)
      const paymentStatusInfo = isPaymentRelatedStatus(dbStatus);

      let currentPage: PageType;
      let currentPageIndex: number;

      if (paymentStatusInfo.isPayment) {
        // For any payment-related status (known or unknown), treat as if we're at zusammenfassung page
        // since payment can only happen after completing all form pages
        currentPage = 'zusammenfassung';
        currentPageIndex = pages.findIndex(page => page === 'zusammenfassung');

        if (currentPageIndex === -1) {
          console.warn(`Certificate type ${certType} does not have zusammenfassung page`);
          return null;
        }

        // Log unknown payment statuses for monitoring
        if (paymentStatusInfo.isUnknown) {
          console.warn(`Unknown payment status encountered: ${dbStatus}. Treating as payment status to preserve payment flow integrity.`);
        }
      } else {
        // For form-based statuses, use the status directly as the current page
        currentPage = dbStatus as PageType;
        currentPageIndex = pages.findIndex(page => page === currentPage);

        if (currentPageIndex === -1) {
          console.warn(`Invalid status page: ${currentPage} for certificate type: ${certType}`);
          return null;
        }
      }

      // Special handling for ZusammenfassungPage and payment statuses: when a certificate has reached
      // 'zusammenfassung' status or any payment status, all previous pages should be marked as visited
      // since it indicates completion of the entire flow
      const isZusammenfassungOrPaymentStatus = currentPage === 'zusammenfassung' || paymentStatusInfo.isPayment;

      // For ZusammenfassungPage or payment statuses, mark all previous pages as visited since it indicates completion
      // For other pages, only mark pages before the current page as visited
      const visitedPages = isZusammenfassungOrPaymentStatus
        ? pages.slice(0, -1) // All pages except zusammenfassung are visited (since zusammenfassung is always last)
        : pages.slice(0, currentPageIndex);

      return {
        visitedPages,
        highestPageReached: isZusammenfassungOrPaymentStatus
          ? pages.length - 2 // All pages except zusammenfassung are completed (zusammenfassung is always last)
          : Math.max(0, currentPageIndex - 1),
        certificateId: certId,
        currentPage,
        actualDbStatus: dbStatus, // Store the actual database status
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Error loading navigation state from database:', error);
      return null;
    }
  }, []);

  // Update certificate status in database with unidirectional progression validation
  const updateCertificateStatus = useCallback(async (certId: string, pageType: PageType) => {
    try {
      // First, get the current status to validate progression
      const { data: currentData, error: fetchError } = await supabase
        .from('energieausweise')
        .select('status, certificate_type')
        .eq('id', certId)
        .single();

      if (fetchError) {
        console.error('Error fetching current certificate status:', fetchError);
        return false;
      }

      if (currentData) {
        const currentStatus = currentData.status;

        // Validate unidirectional progression
        if (currentStatus) {
          // Prevent regression from 'zusammenfassung' to any earlier page
          if (currentStatus === 'zusammenfassung' && pageType !== 'zusammenfassung') {
            console.log(`Database update prevented: Cannot regress from 'zusammenfassung' to '${pageType}'`);
            return false;
          }

          // Prevent regression from payment-related statuses (both known and unknown)
          const currentStatusInfo = isPaymentRelatedStatus(currentStatus);

          if (currentStatusInfo.isPayment) {
            console.log(`Database update prevented: Cannot regress from payment status '${currentStatus}' to '${pageType}'`);
            return false;
          }
        }
      }

      const { error } = await supabase
        .from('energieausweise')
        .update({
          status: pageType,
          updated_at: new Date().toISOString()
        })
        .eq('id', certId);

      if (error) {
        console.error('Error updating certificate status:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error updating certificate status:', error);
      return false;
    }
  }, []);

  // Initialize navigation state when certificate changes
  useEffect(() => {
    const initializeNavigationState = async () => {
      if (!activeCertificateId || !certificateType) {
        setNavigationState(null);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      const state = await loadNavigationStateFromDatabase(activeCertificateId, certificateType);

      if (state) {
        setNavigationState(state);
      } else {
        // Create new navigation state with default status 'objektdaten'
        const newState: NavigationState = {
          visitedPages: [],
          highestPageReached: -1,
          certificateId: activeCertificateId,
          currentPage: 'objektdaten',
          actualDbStatus: 'objektdaten', // Default status for new certificates
          timestamp: Date.now()
        };
        setNavigationState(newState);

        // Set initial status in database
        await updateCertificateStatus(activeCertificateId, 'objektdaten');
      }

      setIsLoading(false);
    };

    initializeNavigationState();
  }, [activeCertificateId, certificateType, loadNavigationStateFromDatabase, updateCertificateStatus]);

  // Mark a page as visited by updating database status
  const markPageAsVisited = useCallback(async (pageType: PageType) => {
    // Early return if required dependencies are not available
    if (!certificateType || !activeCertificateId) return;

    // Don't update if navigation state is still loading to prevent race conditions
    if (isLoading) return;

    const pages = certificateTypePages[certificateType];
    const pageIndex = pages.findIndex(page => page === pageType);

    if (pageIndex === -1) return;

    // Special handling for ZusammenfassungPage: always allow updating to it
    // as it represents completion of all required data entry
    const isZusammenfassungPage = pageType === 'zusammenfassung';

    // UNIDIRECTIONAL STATUS PROGRESSION: Prevent status regression
    // Once a certificate reaches 'zusammenfassung' status, it should never regress
    if (navigationState) {
      const currentPageIndex = pages.findIndex(page => page === navigationState.currentPage);
      const isCurrentlyAtZusammenfassung = navigationState.currentPage === 'zusammenfassung';

      // If currently at 'zusammenfassung' status, prevent any regression to earlier pages
      if (isCurrentlyAtZusammenfassung && !isZusammenfassungPage) {
        console.log(`Status regression prevented: Cannot move from 'zusammenfassung' to '${pageType}'`);
        return;
      }

      // For non-zusammenfassung pages, only allow forward progression
      if (!isZusammenfassungPage && currentPageIndex >= pageIndex) {
        // Don't update if we're already at or beyond this page (except for ZusammenfassungPage)
        return;
      }
    }

    // Update database status
    const success = await updateCertificateStatus(activeCertificateId, pageType);

    if (success) {
      // Update local state to reflect the change
      setNavigationState(prevState => {
        if (!prevState) return prevState;

        // For ZusammenfassungPage, mark all previous pages as visited since it indicates completion
        // For other pages, only mark pages before the current page as visited
        const visitedPages = isZusammenfassungPage
          ? pages.slice(0, -1) // All pages except zusammenfassung are visited (since zusammenfassung is always last)
          : pages.slice(0, pageIndex);

        return {
          ...prevState,
          visitedPages,
          highestPageReached: isZusammenfassungPage
            ? pages.length - 2 // All pages except zusammenfassung are completed (zusammenfassung is always last)
            : Math.max(0, pageIndex - 1),
          currentPage: pageType,
          actualDbStatus: pageType, // Update the actual database status
          timestamp: Date.now()
        };
      });
    }
  }, [certificateType, activeCertificateId, updateCertificateStatus, navigationState, isLoading]);

  // Check if a page is accessible (completed, visited, or current)
  const isPageAccessible = useCallback((pageType: PageType, currentPageType: PageType | null): boolean => {
    // Return false if required dependencies are not available
    if (!navigationState || !certificateType || !currentPageType) return false;

    const pages = certificateTypePages[certificateType];
    const pageIndex = pages.findIndex(page => page === pageType);
    const currentPageIndex = pages.findIndex(page => page === currentPageType);

    if (pageIndex === -1 || currentPageIndex === -1) return false;

    // Check if certificate is in a payment status - if so, all form pages are accessible
    const statusInfo = isPaymentRelatedStatus(navigationState.actualDbStatus);
    const isInPaymentStatus = statusInfo.isPayment;

    if (isInPaymentStatus) {
      // For payment statuses, all pages including zusammenfassung are accessible for editing
      const zusammenfassungIndex = pages.findIndex(page => page === 'zusammenfassung');
      return pageIndex <= zusammenfassungIndex;
    }

    // Current page is always accessible
    if (pageType === currentPageType) return true;

    // Completed pages (before current page) are accessible
    if (pageIndex < currentPageIndex) return true;

    // Previously visited pages are accessible
    if (navigationState.visitedPages.includes(pageType)) return true;

    return false;
  }, [navigationState, certificateType]);

  // Check if a page is completed (before current page)
  const isPageCompleted = useCallback((pageType: PageType, currentPageType: PageType | null): boolean => {
    // Return false if required dependencies are not available
    if (!navigationState || !certificateType || !currentPageType) return false;

    const pages = certificateTypePages[certificateType];
    const pageIndex = pages.findIndex(page => page === pageType);
    const currentPageIndex = pages.findIndex(page => page === currentPageType);

    if (pageIndex === -1 || currentPageIndex === -1) return false;

    // Check if certificate is in a payment status - if so, all form pages are completed
    const statusInfo = isPaymentRelatedStatus(navigationState.actualDbStatus);
    const isInPaymentStatus = statusInfo.isPayment;

    if (isInPaymentStatus) {
      // For payment statuses, all form pages (excluding zusammenfassung) are completed
      const zusammenfassungIndex = pages.findIndex(page => page === 'zusammenfassung');
      return pageIndex < zusammenfassungIndex; // Exclude zusammenfassung itself from being marked as completed
    }

    // For non-payment statuses, use the standard logic (pages before current page are completed)
    return pageIndex < currentPageIndex;
  }, [navigationState, certificateType]);

  // Check if a page is visited but not completed
  const isPageVisited = useCallback((pageType: PageType, currentPageType: PageType | null): boolean => {
    // Return false if required dependencies are not available
    if (!navigationState || !certificateType || !currentPageType) return false;

    const isCompleted = isPageCompleted(pageType, currentPageType);
    const isVisited = navigationState.visitedPages.includes(pageType);

    return isVisited && !isCompleted && pageType !== currentPageType;
  }, [navigationState, certificateType, isPageCompleted]);

  // Check if the certificate is completed (has reached ZusammenfassungPage)
  const isCertificateCompleted = useCallback((currentPageType: PageType | null): boolean => {
    return currentPageType === 'zusammenfassung';
  }, []);

  // Clear navigation state (reset certificate to first page)
  const clearNavigationState = useCallback(async () => {
    if (activeCertificateId) {
      await updateCertificateStatus(activeCertificateId, 'objektdaten');
      setNavigationState(null);
    }
  }, [activeCertificateId, updateCertificateStatus]);

  return {
    navigationState,
    markPageAsVisited,
    isPageAccessible,
    isPageCompleted,
    isPageVisited,
    isCertificateCompleted,
    clearNavigationState,
    isLoading
  };
};
